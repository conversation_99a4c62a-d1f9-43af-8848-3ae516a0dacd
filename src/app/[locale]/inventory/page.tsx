'use client';

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  useGetInventoryItemsQuery,
  useCreateInventoryItemMutation,
  useUpdateInventoryItemMutation,
  useDeleteInventoryItemMutation
} from '@/redux/services/inventoryApi';
import { InventoryItem, InventoryItemStatus } from '@/lib/types';
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Plus, Grid, List, Eye, Edit, Trash2 } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { FileUpload } from "@/components/ui/file-upload";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

// Advanced pagination components
import { PaginatedTable, type TableColumn } from '@/components/common';
import { useAdvancedPagination } from '@/hooks';

// Formatters
import { useFormatters } from '@/hooks/useFormatters';

export default function InventoryPage() {
  // Formatters
  const { formatCurrency, formatDate } = useFormatters();

  // View mode state
  const [viewMode, setViewMode] = useState<'grid' | 'table'>('table');

  // Advanced pagination for inventory items
  const { state, actions } = useAdvancedPagination({
    initialSort: {
      sortBy: 'created_at',
      sortOrder: 'desc'
    },
    initialLimit: 10,
    persist: true,
    persistKey: 'inventory-items'
  });

  // Query parameters
  const { data, error, isLoading, refetch, isFetching } = useGetInventoryItemsQuery({
    page: state.page,
    limit: state.limit,
    search: state.search
  });

  // Apply client-side sorting since API doesn't support it
  const sortedInventoryItems = useMemo(() => {
    const items = data?.items || [];
    if (!state.sortBy) return items;

    return [...items].sort((a, b) => {
      let aValue: any;
      let bValue: any;

      // Map sort keys to actual item properties
      switch (state.sortBy) {
        case 'name':
          aValue = a.name;
          bValue = b.name;
          break;
        case 'unitCost':
          aValue = Number(a.unitCost);
          bValue = Number(b.unitCost);
          break;
        case 'quantityOnHand':
          aValue = Number(a.quantityOnHand);
          bValue = Number(b.quantityOnHand);
          break;
        case 'status':
          aValue = a.status;
          bValue = b.status;
          break;
        case 'created_at':
          aValue = new Date(a.createdAt);
          bValue = new Date(b.createdAt);
          break;
        default:
          return 0;
      }

      if (aValue < bValue) return state.sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return state.sortOrder === 'asc' ? 1 : -1;
      return 0;
    });
  }, [data?.items, state.sortBy, state.sortOrder]);

  const inventoryItems = sortedInventoryItems;
  const paginationInfo = data?.pagination;

  const [createInventoryItem, { isLoading: isCreating }] = useCreateInventoryItemMutation();
  const [updateInventoryItem, { isLoading: isUpdating }] = useUpdateInventoryItemMutation();
  const [deleteInventoryItem, { isLoading: isDeleting }] = useDeleteInventoryItemMutation();

  // Dialog states
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<InventoryItem | null>(null);

  // Form states
  const [newItem, setNewItem] = useState({
    name: '',
    sku: '',
    unit_of_measure: '',
    unit_cost: 0,
    quantity_on_hand: 0,
    asset_account_id: '********-0000-0000-0000-************', // Using a valid UUID format for mock
    image_url: '', // This will be mapped to imageUrl in the service
  });

  const [editItem, setEditItem] = useState<Partial<InventoryItem>>({
    name: '',
    sku: '',
    unit_of_measure: '',
    unit_cost: 0,
    quantity_on_hand: 0,
    image_url: '',
    status: InventoryItemStatus.Active,
  });

  // Error states
  const [addError, setAddError] = useState<React.ReactNode | null>(null);
  const [editError, setEditError] = useState<React.ReactNode | null>(null);

  // Asset accounts state
  const [assetAccounts, setAssetAccounts] = useState<any[]>([]);
  const [loadingAccounts, setLoadingAccounts] = useState(true);

  // Handle view item
  const handleViewItem = useCallback((item: InventoryItem) => {
    // For now, just open edit dialog in view mode
    handleEditClick(item);
  }, []);

  // Handle edit button click
  const handleEditClick = (item: any) => {
    setSelectedItem(item);
    setEditItem({
      name: item.name,
      sku: item.sku,
      unit_of_measure: item.unitOfMeasure,
      unit_cost: Number(item.unitCost),
      quantity_on_hand: Number(item.quantityOnHand),
      image_url: item.imageUrl || '',
      status: item.status,
    });
    setIsEditDialogOpen(true);
  };

  // Handle delete button click
  const handleDeleteClick = (item: any) => {
    setSelectedItem(item);
    setIsDeleteDialogOpen(true);
  };

  // Define table columns for inventory items
  const inventoryColumns: TableColumn<InventoryItem>[] = useMemo(() => [
    {
      key: 'name',
      header: 'Name',
      sortable: true,
      render: (_, item) => (
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 relative bg-muted/50 rounded flex items-center justify-center overflow-hidden">
            {item.imageUrl ? (
              <img
                src={item.imageUrl}
                alt={item.name}
                className="w-full h-full object-cover"
                onError={(e) => {
                  (e.target as HTMLImageElement).src = 'https://placehold.co/40x40?text=No+Image';
                }}
              />
            ) : (
              <span className="text-xs text-muted-foreground">No img</span>
            )}
          </div>
          <div>
            <div className="font-medium">{item.name}</div>
            <div className="text-sm text-muted-foreground">SKU: {item.sku}</div>
          </div>
        </div>
      )
    },
    {
      key: 'unitOfMeasure',
      header: 'Unit',
      sortable: true,
    },
    {
      key: 'unitCost',
      header: 'Unit Cost',
      sortable: true,
      render: (_, item) => (
        <div className="text-right font-medium">
          {formatCurrency(Number(item.unitCost))}
        </div>
      )
    },
    {
      key: 'quantityOnHand',
      header: 'Quantity',
      sortable: true,
      render: (_, item) => (
        <div className="text-right">
          {Number(item.quantityOnHand)} {item.unitOfMeasure}
        </div>
      )
    },
    {
      key: 'status',
      header: 'Status',
      sortable: true,
      render: (_, item) => (
        <Badge variant={item.status === InventoryItemStatus.Active ? 'default' : 'secondary'}>
          {item.status}
        </Badge>
      )
    },
    {
      key: 'totalValue',
      header: 'Total Value',
      sortable: false,
      render: (_, item) => (
        <div className="text-right font-medium">
          {formatCurrency(Number(item.unitCost) * Number(item.quantityOnHand))}
        </div>
      )
    },
    {
      key: 'actions',
      header: 'Actions',
      sortable: false,
      render: (_, item) => (
        <div className="flex items-center justify-end space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleViewItem(item)}
          >
            <Eye className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEditClick(item)}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleDeleteClick(item)}
            className="text-red-600 hover:text-red-700"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      )
    }
  ], [formatCurrency, handleViewItem]);

  // Fetch asset accounts on component mount
  useEffect(() => {
    const fetchAssetAccounts = async () => {
      try {
        setLoadingAccounts(true);
        const response = await fetch('/api/coa/asset-accounts');
        if (response.ok) {
          const data = await response.json();
          setAssetAccounts(data.accounts || []);
        } else {
          console.error('Failed to fetch asset accounts:', response.status);
        }
      } catch (error) {
        console.error('Error fetching asset accounts:', error);
      } finally {
        setLoadingAccounts(false);
      }
    };

    fetchAssetAccounts();
  }, []);

  // Reset add form
  const resetAddForm = () => {
    setNewItem({
      name: '',
      sku: '',
      unit_of_measure: '',
      unit_cost: 0,
      quantity_on_hand: 0,
      asset_account_id: assetAccounts.length > 0 ? assetAccounts[0].id : '********-0000-0000-0000-************',
      image_url: '', // This will be mapped to imageUrl in the service
    });
    setAddError(null);
  };



  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    if (!selectedItem) return;

    try {
      await deleteInventoryItem(selectedItem.id).unwrap();
      // Refresh the data after successful deletion
      refetch();
      setIsDeleteDialogOpen(false);
      setSelectedItem(null);
    } catch (error) {
      console.error('Failed to delete inventory item:', error);
    }
  };

  // Handle input changes for add form
  const handleAddInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setNewItem({
      ...newItem,
      [name]: name === 'unit_cost' || name === 'quantity_on_hand' ? parseFloat(value) || 0 : value,
    });
  };

  // Handle image upload for add form
  const handleAddImageUpload = (url: string) => {
    setNewItem({
      ...newItem,
      image_url: url,
    });
  };

  // Handle input changes for edit form
  const handleEditInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setEditItem({
      ...editItem,
      [name]: name === 'unit_cost' || name === 'quantity_on_hand' ? parseFloat(value) || 0 : value,
    });
  };

  // Handle image upload for edit form
  const handleEditImageUpload = (url: string) => {
    setEditItem({
      ...editItem,
      image_url: url,
    });
  };

  // Handle status change for edit form
  const handleStatusChange = (value: string) => {
    setEditItem({
      ...editItem,
      status: value as InventoryItemStatus,
    });
  };

  // Handle add form submission
  const handleAddSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setAddError(null);

    try {
      await createInventoryItem({
        ...newItem,
        status: InventoryItemStatus.Active,
      }).unwrap();

      // Reset form and close dialog
      resetAddForm();
      setIsAddDialogOpen(false);

      // Refresh the data after successful creation
      refetch();
    } catch (err: unknown) {
      const error = err as { status?: number; message?: string };
      console.error('Failed to create inventory item:', err);

      // Check if it's a duplicate SKU error (409 Conflict)
      if (error.status === 409) {
        const uniqueSku = generateUniqueSku(newItem.sku);
        setAddError(
          <div>
            SKU &quot;{newItem.sku}&quot; already exists.
            <button
              type="button"
              className="ml-2 text-blue-500 hover:underline"
              onClick={() => {
                setNewItem({...newItem, sku: uniqueSku});
              }}
            >
              Try &quot;{uniqueSku}&quot; instead
            </button>
          </div>
        );
      } else {
        setAddError(error.message || 'Failed to create inventory item');
      }
    }
  };

  // Handle edit form submission
  const handleEditSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setEditError(null);

    if (!selectedItem) {
      setEditError('No item selected for editing');
      return;
    }

    try {
      await updateInventoryItem({
        id: selectedItem.id,
        body: editItem,
      }).unwrap();

      // Close dialog and reset selection
      setIsEditDialogOpen(false);
      setSelectedItem(null);

      // Refresh the data after successful update
      refetch();
    } catch (err: unknown) {
      const error = err as { status?: number; message?: string };
      console.error('Failed to update inventory item:', err);

      // Check if it's a duplicate SKU error (409 Conflict)
      if (error.status === 409) {
        const currentSku = editItem.sku || '';
        const uniqueSku = generateUniqueSku(currentSku);
        setEditError(
          <div>
            SKU &quot;{currentSku}&quot; already exists.
            <button
              type="button"
              className="ml-2 text-blue-500 hover:underline"
              onClick={() => {
                setEditItem({...editItem, sku: uniqueSku});
              }}
            >
              Try &quot;{uniqueSku}&quot; instead
            </button>
          </div>
        );
      } else {
        setEditError(error.message || 'Failed to update inventory item');
      }
    }
  };

  // Generate a unique SKU suggestion by adding a suffix
  const generateUniqueSku = (sku: string): string => {
    // Check if the SKU already has a numeric suffix
    const match = sku.match(/^(.+?)(-\d+)?$/);
    if (match) {
      const baseSku = match[1];
      const currentSuffix = match[2] ? parseInt(match[2].substring(1)) : 0;
      return `${baseSku}-${currentSuffix + 1}`;
    }
    // If no match or no suffix, add -1
    return `${sku}-1`;
  };

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">Inventory Management</h1>
          <p className="text-muted-foreground">Manage your inventory items and track stock levels</p>
        </div>

        <div className="flex items-center space-x-2">
          {/* View Mode Toggle */}
          <div className="flex items-center border rounded-lg p-1">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('grid')}
            >
              <Grid className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'table' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('table')}
            >
              <List className="h-4 w-4" />
            </Button>
          </div>

          {/* Add New Item Button */}
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
            <Button onClick={() => setIsAddDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add New Item
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Add New Inventory Item</DialogTitle>
              <DialogDescription>
                Enter the details for the new inventory item. Click save when you&apos;re done.
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleAddSubmit}>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="name" className="text-right">
                    Name*
                  </Label>
                  <Input
                    id="name"
                    name="name"
                    value={newItem.name}
                    onChange={handleAddInputChange}
                    className="col-span-3"
                    required
                  />
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="sku" className="text-right">
                    SKU*
                  </Label>
                  <Input
                    id="sku"
                    name="sku"
                    value={newItem.sku}
                    onChange={handleAddInputChange}
                    className="col-span-3"
                    required
                  />
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="unit_of_measure" className="text-right">
                    Unit*
                  </Label>
                  <Input
                    id="unit_of_measure"
                    name="unit_of_measure"
                    value={newItem.unit_of_measure}
                    onChange={handleAddInputChange}
                    className="col-span-3"
                    required
                  />
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="unit_cost" className="text-right">
                    Unit Cost*
                  </Label>
                  <Input
                    id="unit_cost"
                    name="unit_cost"
                    type="number"
                    step="0.01"
                    min="0"
                    value={newItem.unit_cost}
                    onChange={handleAddInputChange}
                    className="col-span-3"
                    required
                  />
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="quantity_on_hand" className="text-right">
                    Quantity*
                  </Label>
                  <Input
                    id="quantity_on_hand"
                    name="quantity_on_hand"
                    type="number"
                    step="0.01"
                    min="0"
                    value={newItem.quantity_on_hand}
                    onChange={handleAddInputChange}
                    className="col-span-3"
                    required
                  />
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="asset_account_id" className="text-right">
                    Asset Account*
                  </Label>
                  <Select
                    value={newItem.asset_account_id}
                    onValueChange={(value) => setNewItem({...newItem, asset_account_id: value})}
                  >
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder={loadingAccounts ? "Loading accounts..." : "Select an asset account"} />
                    </SelectTrigger>
                    <SelectContent>
                      {loadingAccounts ? (
                        <SelectItem value="loading" disabled>Loading accounts...</SelectItem>
                      ) : assetAccounts.length > 0 ? (
                        assetAccounts.map((account) => (
                          <SelectItem key={account.id} value={account.id}>
                            {account.account_code} - {account.account_name}
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="no-accounts" disabled>No asset accounts found</SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="image_url" className="text-right">
                    Image
                  </Label>
                  <div className="col-span-3">
                    <FileUpload
                      onUploadComplete={handleAddImageUpload}
                      currentImageUrl={newItem.image_url}
                      maxSizeMB={2}
                      buttonText="Upload Item Image"
                    />
                    <p className="text-sm text-muted-foreground mt-1">
                      Upload an image of this inventory item for easier identification
                    </p>
                  </div>
                </div>

                {addError && <p className="col-span-4 text-red-500 text-sm">{addError}</p>}
              </div>
              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    resetAddForm();
                    setIsAddDialogOpen(false);
                  }}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isCreating}>
                  {isCreating ? 'Adding...' : 'Add Item'}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
        </div>
      </div>

      {/* Main Content */}
      <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as 'grid' | 'table')}>
        <div className="flex items-center justify-between mb-6">
          <TabsList>
            <TabsTrigger value="grid">Grid View</TabsTrigger>
            <TabsTrigger value="table">Table View</TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="grid">
          {isLoading && <p>Loading inventory items...</p>}
          {error && <p className="text-red-500">Error loading inventory items</p>}

          {!isLoading && !error && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {inventoryItems.length > 0 ? (
              inventoryItems.map((item) => (
                <Card key={item.id} className="overflow-hidden">
                  <div className="w-full h-40 overflow-hidden bg-muted/50 flex items-center justify-center">
                    {item.imageUrl ? (
                      <img
                        src={item.imageUrl}
                        alt={item.name}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          // Replace with placeholder if image fails to load
                          (e.target as HTMLImageElement).src = 'https://placehold.co/400x200?text=No+Image';
                        }}
                      />
                    ) : (
                      <div className="text-center text-muted-foreground">
                        <div className="mb-2">No image available</div>
                        <div className="text-xs">Upload an image in edit mode</div>
                      </div>
                    )}
                  </div>
                  <CardHeader className="pb-2">
                    <CardTitle>{item.name}</CardTitle>
                    <CardDescription>SKU: {item.sku}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <p className="text-sm text-muted-foreground">Unit Cost</p>
                        <p className="font-medium">{formatCurrency(Number(item.unitCost))}</p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">Quantity</p>
                        <p className="font-medium">{Number(item.quantityOnHand)} {item.unitOfMeasure}</p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">Status</p>
                        <p className="font-medium">{item.status}</p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">Total Value</p>
                        <p className="font-medium">{formatCurrency(Number(item.unitCost) * Number(item.quantityOnHand))}</p>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="bg-muted/50 pt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="ml-auto"
                      onClick={() => handleEditClick(item)}
                    >
                      Edit Item
                    </Button>
                  </CardFooter>
                </Card>
              ))
            ) : (
              <div className="col-span-full text-center p-8 bg-muted rounded-lg">
                <p className="text-muted-foreground">No inventory items found.</p>
                <Button
                  variant="outline"
                  className="mt-4"
                  onClick={() => setIsAddDialogOpen(true)}
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Add Your First Item
                </Button>
              </div>
            )}
          </div>
          )}
        </TabsContent>

        <TabsContent value="table">
          <PaginatedTable
            data={inventoryItems}
            columns={inventoryColumns}
            paginationInfo={paginationInfo}
            isLoading={isLoading}
            error={error ? 'Failed to load inventory items' : null}
            isFetching={isFetching}

            // Search functionality
            searchable
            searchPlaceholder="Search inventory items..."
            searchValue={state.search}
            onSearchChange={actions.setSearch}

            // Sorting
            sortBy={state.sortBy}
            sortOrder={state.sortOrder}
            onSortChange={(column: string) => {
              // Toggle sort order if same column, otherwise default to asc
              const newOrder = state.sortBy === column && state.sortOrder === 'asc' ? 'desc' : 'asc';
              actions.setSingleSort(column, newOrder);
            }}

            // Pagination
            currentPage={state.page}
            pageSize={state.limit}
            onPageChange={actions.setPage}
            onPageSizeChange={actions.setLimit}

            // Display
            title="Inventory Items"
            emptyMessage="No inventory items found"
          />
        </TabsContent>
      </Tabs>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Edit Inventory Item</DialogTitle>
            <DialogDescription>
              Update the details for {selectedItem?.name}. Click save when you&apos;re done.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleEditSubmit}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-name" className="text-right">
                  Name*
                </Label>
                <Input
                  id="edit-name"
                  name="name"
                  value={editItem.name}
                  onChange={handleEditInputChange}
                  className="col-span-3"
                  required
                />
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-sku" className="text-right">
                  SKU*
                </Label>
                <Input
                  id="edit-sku"
                  name="sku"
                  value={editItem.sku}
                  onChange={handleEditInputChange}
                  className="col-span-3"
                  required
                />
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-unit_of_measure" className="text-right">
                  Unit*
                </Label>
                <Input
                  id="edit-unit_of_measure"
                  name="unit_of_measure"
                  value={editItem.unit_of_measure}
                  onChange={handleEditInputChange}
                  className="col-span-3"
                  required
                />
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-unit_cost" className="text-right">
                  Unit Cost*
                </Label>
                <Input
                  id="edit-unit_cost"
                  name="unit_cost"
                  type="number"
                  step="0.01"
                  min="0"
                  value={editItem.unit_cost}
                  onChange={handleEditInputChange}
                  className="col-span-3"
                  required
                />
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-quantity_on_hand" className="text-right">
                  Quantity*
                </Label>
                <Input
                  id="edit-quantity_on_hand"
                  name="quantity_on_hand"
                  type="number"
                  step="0.01"
                  min="0"
                  value={editItem.quantity_on_hand}
                  onChange={handleEditInputChange}
                  className="col-span-3"
                  required
                />
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-image_url" className="text-right">
                  Image
                </Label>
                <div className="col-span-3">
                  <FileUpload
                    onUploadComplete={handleEditImageUpload}
                    currentImageUrl={editItem.image_url}
                    maxSizeMB={2}
                    buttonText="Upload Item Image"
                  />
                  <p className="text-sm text-muted-foreground mt-1">
                    Upload an image of this inventory item for easier identification
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-status" className="text-right">
                  Status
                </Label>
                <Select
                  value={editItem.status}
                  onValueChange={handleStatusChange}
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={InventoryItemStatus.Active}>Active</SelectItem>
                    <SelectItem value={InventoryItemStatus.Inactive}>Inactive</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {editError && <p className="col-span-4 text-red-500 text-sm">{editError}</p>}
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setIsEditDialogOpen(false);
                  setSelectedItem(null);
                }}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isUpdating}>
                {isUpdating ? 'Saving...' : 'Save Changes'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Delete Inventory Item</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete {selectedItem?.name}? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <p className="text-sm text-muted-foreground">
              This will permanently remove the item from your inventory.
            </p>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setIsDeleteDialogOpen(false);
                setSelectedItem(null);
              }}
            >
              Cancel
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={handleDeleteConfirm}
              disabled={isDeleting}
            >
              {isDeleting ? 'Deleting...' : 'Delete Item'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
